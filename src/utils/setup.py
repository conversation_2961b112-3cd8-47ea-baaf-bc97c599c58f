"""Setup utilities for ReelStonks project.

This module provides utilities for setting up the project directory structure
and ensuring all necessary directories exist before running the application.
"""

from src.logger import get_logger
from src.utils.paths import (
    get_project_root,
    get_assets_dir,
    get_animations_dir,
    get_data_dir,
    ensure_dir_exists,
    get_timestamped_animations_dir,
)


LOGGER = get_logger(__name__)


class ProjectSetup:
    """Handles project directory setup and initialization."""

    def __init__(self):
        """Initialize the ProjectSetup instance."""
        self.project_root = get_project_root()
        self.assets_dir = get_assets_dir()
        self.animations_dir = get_animations_dir()
        self.data_dir = get_data_dir()

        self.required_directories = [
            self.assets_dir,
            self.animations_dir,
            self.data_dir,
            get_timestamped_animations_dir(),
        ]

    def setup_directories(self, verbose: bool = True) -> bool:
        """Set up all required project directories.

        Creates all necessary directories for the project to function properly.
        If directories already exist, they are left unchanged.

        Args:
            verbose (bool, optional): Whether to log setup progress. Defaults to True.

        Returns:
            bool: True if setup was successful, False otherwise.
        """
        if verbose:
            LOGGER.activate()
            LOGGER.info("🚀 Setting up ReelStonks project directories...")

        try:
            created_dirs = []
            existing_dirs = []

            for directory in self.required_directories:
                if directory.exists():
                    existing_dirs.append(directory)
                    if verbose:
                        LOGGER.info(
                            f"✅ Directory already exists: {directory.relative_to(self.project_root)}"
                        )
                else:
                    ensure_dir_exists(directory)
                    created_dirs.append(directory)
                    if verbose:
                        LOGGER.info(
                            f"📁 Created directory: {directory.relative_to(self.project_root)}"
                        )

            if verbose:
                if created_dirs:
                    LOGGER.info(
                        f"🎉 Successfully created {len(created_dirs)} new directories"
                    )
                if existing_dirs:
                    LOGGER.info(f"ℹ️  Found {len(existing_dirs)} existing directories")
                LOGGER.info(
                    "✨ Project setup complete! Ready to create amazing investment animations."
                )

            return True

        except Exception as e:
            if verbose:
                LOGGER.error(f"❌ Failed to set up project directories: {e}")
            return False

    def verify_setup(self) -> bool:
        """Verify that all required directories exist.

        Returns:
            bool: True if all required directories exist, False otherwise.
        """
        for directory in self.required_directories:
            if not directory.exists():
                return False
        return True

    def get_setup_status(self) -> dict:
        """Get the current setup status.

        Returns:
            dict: Dictionary containing setup status information with keys:
                - 'is_setup': bool indicating if setup is complete
                - 'missing_dirs': list of missing directory paths
                - 'existing_dirs': list of existing directory paths
        """
        missing_dirs = []
        existing_dirs = []

        for directory in self.required_directories:
            if directory.exists():
                existing_dirs.append(directory)
            else:
                missing_dirs.append(directory)

        return {
            "is_setup": len(missing_dirs) == 0,
            "missing_dirs": missing_dirs,
            "existing_dirs": existing_dirs,
            "project_root": self.project_root,
        }

    def create_gitkeep_files(self) -> None:
        """Create .gitkeep files in empty directories.

        This ensures that empty directories are tracked by git while
        still ignoring the actual content files (as specified in .gitignore).
        """
        for directory in self.required_directories:
            if directory.exists():
                gitkeep_file = directory / ".gitkeep"
                if not gitkeep_file.exists():
                    gitkeep_file.touch()
                    LOGGER.info(
                        f"📝 Created .gitkeep in {directory.relative_to(self.project_root)}"
                    )


def setup_project(verbose: bool = True) -> bool:
    """Convenience function to set up the project.

    Args:
        verbose (bool, optional): Whether to log setup progress. Defaults to True.

    Returns:
        bool: True if setup was successful, False otherwise.
    """
    setup = ProjectSetup()
    return setup.setup_directories(verbose=verbose)


def verify_project_setup() -> bool:
    """Convenience function to verify project setup.

    Returns:
        bool: True if project is properly set up, False otherwise.
    """
    setup = ProjectSetup()
    return setup.verify_setup()


if __name__ == "__main__":
    # Allow running this module directly for setup
    setup_project(verbose=True)
