"""
This module contains utility functions for working with dictionaries.
"""

from typing import Any, List, Union
from src.utils.enum import DictConditionOperator


def get_value_by_key_condition(
    d: dict,
    contains: Union[List[Any], Any],
    operator: DictConditionOperator = DictConditionOperator.AND,
) -> Any:
    """
    Gets the first value from a dictionary where the key meets a condition.

    Args:
        d (dict): The dictionary to search, with tuples as keys.
        contains (Union[List[Any], Any]): An item or list of items to check
            for in the dictionary keys.
        operator (DictConditionOperator, optional): The logical operator to use.
            - "and": The key must contain all items in `contains`.
            - "or": The key must contain at least one item from `contains`.
            Defaults to AND.

    Returns:
        Any: The value of the first matching key, or None if no match is found.
    """

    if isinstance(contains, (set, tuple)):
        contains = list(contains)

    # Normalize the 'contains' argument to always be a list
    if not isinstance(contains, list):
        return d[contains]

    # Iterate through each key-value pair in the dictionary
    for key, value in d.items():
        if operator == DictConditionOperator.AND:
            # Check if ALL items in 'contains' are present in the key
            if all(item in key for item in contains):
                return value
        elif operator == DictConditionOperator.OR:
            # Check if ANY item in 'contains' is present in the key
            if any(item in key for item in contains):
                return value

    # If the loop completes without finding a match, return None
    return None
