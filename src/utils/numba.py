"""
This module contains the optimized investment calculation function using Numba.
"""

from numba import njit, types
import numpy as np


_invest_signature = types.Tuple(
    (
        types.float64[:],
        types.float64[:],
        types.float64[:],
        types.float64[:],
        types.float64[:],
    )
)(
    types.float64[:],
    types.float64[:],
    types.float64,
    types.boolean[:],
    types.boolean[:],
    types.float64,
    types.float64,
    types.boolean,
)


@njit(_invest_signature, cache=True)
def _invest(
    price: np.ndarray,
    dividends: np.ndarray,
    investment_amount: float,
    investment_periods: np.ndarray,
    yearly_periods: np.ndarray,
    tax_rate: float,
    tax_free_return_threshold_per_annu: float,
    reinvest_dividends: bool,
) -> np.ndarray:

    n = len(price)

    invested_cash = np.zeros(n)
    number_of_stocks = np.zeros(n)
    paid_gross_dividends = np.zeros(n)
    paid_net_dividends = np.zeros(n)
    cumulative_cash_dividends = np.zeros(n)  # Track cumulative cash dividends

    wealth = np.zeros(n)

    _dividends_per_annum = 0

    for i in range(n):

        # Get number of stocks held the previous day
        prev_stocks = number_of_stocks[i - 1] if i > 0 else 0.0

        # Get previous cumulative cash dividends
        prev_cumulative_cash = cumulative_cash_dividends[i - 1] if i > 0 else 0.0

        # Check if we are in a new year
        if yearly_periods[i]:
            # Reset dividends per annum
            _dividends_per_annum = 0

        # Compute gross dividends based on dividends paid today and stocks held yesterday
        paid_gross_dividends[i] = prev_stocks * dividends[i]

        # Get dividends before and after today
        dividends_before_today = _dividends_per_annum
        dividends_after_today = dividends_before_today + paid_gross_dividends[i]

        # Define taxable amount if dividends after today exceed the threshold
        taxable_amount = 0.0
        if dividends_after_today > tax_free_return_threshold_per_annu:
            # Calculate the portion of this dividend that is over the threshold
            taxable_amount = dividends_after_today - max(
                dividends_before_today, tax_free_return_threshold_per_annu
            )

        # Calculate tax paid today on the taxable amount
        tax_paid = taxable_amount * tax_rate
        # Calculate net dividends after tax
        paid_net_dividends[i] = paid_gross_dividends[i] - tax_paid
        # Update dividends per annum
        _dividends_per_annum = dividends_after_today

        # Handle dividend reinvestment or accumulation
        if reinvest_dividends:
            # Reinvest dividends immediately by buying more shares
            if paid_net_dividends[i] > 0:
                additional_shares = paid_net_dividends[i] / price[i]
                number_of_stocks[i] = prev_stocks + additional_shares
            else:
                number_of_stocks[i] = prev_stocks
            # No cash dividends accumulated
            cumulative_cash_dividends[i] = prev_cumulative_cash
        else:
            # Don't reinvest - accumulate cash dividends
            number_of_stocks[i] = prev_stocks
            cumulative_cash_dividends[i] = prev_cumulative_cash + paid_net_dividends[i]

        # Handle regular investment purchases
        if investment_periods[i]:
            # Store regular investment amount
            invested_cash[i] = investment_amount
            # Buy additional shares with regular investment
            additional_shares_from_investment = investment_amount / price[i]
            number_of_stocks[i] += additional_shares_from_investment

        # Compute wealth as stock value plus accumulated cash dividends
        wealth[i] = (number_of_stocks[i] * price[i]) + cumulative_cash_dividends[i]

    return (
        wealth,
        invested_cash,
        number_of_stocks,
        paid_gross_dividends,
        paid_net_dividends,
    )
