def construct_string_title(
    company_names: list[str], investment_amount: int, years_ago: int, interval: str
):
    """Constructs a descriptive title string for investment analysis.

    Creates a formatted title string that describes a hypothetical investment
    scenario, including the companies invested in, investment amount, frequency,
    and time period.

    Args:
        company_names (list[str]): List of company names to include in the title.
            If more than 2 companies, they will be formatted as "A, B, and C".
            If 2 companies, they will be formatted as "A and B".
            If 1 company, just the company name is used.
        investment_amount (int): The dollar amount invested at each interval.
        years_ago (int): The number of years ago the investment period started.
        interval (str): The investment frequency (e.g., "Month", "Week", "Year").

    Returns:
        str: A formatted title string describing the investment scenario.
            Example: "What if you had invested $1,000 each month starting 10 years ago in Apple and Microsoft?"
    """

    if len(company_names) > 2:
        ticker_str = f"{', '.join(company_names[:-1])}, and {company_names[-1]}"
    elif len(company_names) == 2:
        ticker_str = " and ".join(company_names)
    else:
        ticker_str = company_names[0]

    return f"What if you had invested ${investment_amount:,} each {interval.lower()} starting {years_ago} years ago in {ticker_str}?"
