class InvestmentStrategy:
    """Represents a single investment strategy configuration."""

    def __init__(
        self,
        strategy_id: str,
        ticker_symbol: str,
        display_name: str,
        reinvest_dividends: bool = False,
        investment_timing: str = "regular",  # "regular", "peaks", "lows"
        **kwargs,
    ):
        """Initialize an investment strategy.

        Args:
            strategy_id (str): Unique identifier for this strategy.
            ticker_symbol (str): The actual ticker symbol to fetch data for.
            display_name (str): Human-readable name for display in charts.
            reinvest_dividends (bool): Whether to reinvest dividends.
            investment_timing (str): When to invest ("regular", "peaks", "lows").
            **kwargs: Additional strategy-specific parameters.
        """
        self.strategy_id = strategy_id
        self.ticker_symbol = ticker_symbol
        self.display_name = display_name
        self.reinvest_dividends = reinvest_dividends
        self.investment_timing = investment_timing
        self.extra_params = kwargs

    def __repr__(self):
        return f"InvestmentStrategy(id='{self.strategy_id}', ticker='{self.ticker_symbol}', name='{self.display_name}')"
