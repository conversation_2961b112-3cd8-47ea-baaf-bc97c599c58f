# bot/telegram.py
import requests
from src.logger import get_logger
from src.bot.telegram.constants import (
    TELEGRAM_TOKEN,
    TELEGRAM_CHAT_ID,
    TELEGRAM_API_BASE_URL,
    TELEGRAM_REQUEST_TIMEOUT,
    TELEGRAM_ENABLED,
)
from src.bot.telegram._rate_limiter import TelegramRateLimiter


LOGGER = get_logger(__file__)
LOGGER.activate()


class TelegramManager:
    """
    Manages sending messages and videos to a Telegram chat via a bot,
    using centralized configuration from constants.
    """

    def __init__(self, token: str = TELEGRAM_TOKEN, chat_id: str = TELEGRAM_CHAT_ID):
        """
        Initializes the TelegramManager with the bot token and chat ID.

        Args:
            token (str): The Telegram Bot API token.
            chat_id (str): The target chat ID.
        """
        # Use the new constants for validation
        if not TELEGRAM_ENABLED:
            LOGGER.error("❌ Telegram is not enabled in the configuration")
            return

        self.rate_limiter = TelegramRateLimiter()
        self.token = token
        self.chat_id = chat_id
        # Construct the base URL from constants
        self.base_url = f"{TELEGRAM_API_BASE_URL}{self.token}"
        self.last_update_id = 0  # Track last processed update for polling
        LOGGER.info("🤖 ReelStonks Telegram Bot Initialized")

    def send_message(self, message: str) -> bool:
        """
        Sends a text message to the configured Telegram chat.

        Args:
            message (str): The text message to send.

        Returns:
            bool: True if the message was sent successfully, False otherwise.
        """
        if not TELEGRAM_ENABLED:
            LOGGER.error(
                "❌ Telegram bot token or chat ID is missing. Cannot send message."
            )
            return False

        # Construct the full URL from constants
        url = f"{self.base_url}/sendMessage"
        payload = {"chat_id": self.chat_id, "text": message}
        try:
            # Use the rate limiter from constants
            self.rate_limiter.wait_if_needed()
            # Use the timeout from constants
            response = requests.post(
                url, json=payload, timeout=TELEGRAM_REQUEST_TIMEOUT
            )
            response.raise_for_status()  # Raise an HTTPError for bad responses
            LOGGER.info(f"✔️  Message sent successfully")
            return True
        except requests.exceptions.RequestException as e:
            LOGGER.error(f"❌ Failed to send message: {e}")
            return False

    def send_video(self, video_path: str, caption: str = "") -> bool:
        """Sends a video file to the configured Telegram chat.

        Args:
            video_path (str): The local file path to the video.
            caption (str, optional): A caption for the video. Defaults to "".

        Returns:
            bool: True if the video was sent successfully, False otherwise.
        """
        if not TELEGRAM_ENABLED:
            LOGGER.error(
                "❌ Telegram bot token or chat ID is missing, cannot send video."
            )
            return False

        # The endpoint for sending videos
        url = f"{self.base_url}/sendVideo"
        payload = {"chat_id": self.chat_id, "caption": caption}
        try:
            # Use the rate limiter from constants
            self.rate_limiter.wait_if_needed()
            # Open the video file in binary mode
            with open(video_path, "rb") as video_file:
                files = {"video": video_file}
                # Use a longer timeout for file uploads as it's not in constants
                response = requests.post(url, data=payload, files=files, timeout=60)
                response.raise_for_status()
            LOGGER.info(
                f"🎥 Video '{video_path}' sent successfully to chat ID {self.chat_id}."
            )
            return True
        except FileNotFoundError:
            LOGGER.error(f"❌ Video file not found at path: {video_path}")
            return False
        except requests.exceptions.RequestException as e:
            LOGGER.error(f"❌ Failed to send video: {e}")
            return False

    def _get_updates(self) -> list:
        """
        Get updates from Telegram API for polling messages.

        Returns:
            list: List of update objects from Telegram API.
        """
        if not TELEGRAM_ENABLED:
            return []

        url = f"{self.base_url}/getUpdates"
        params = {
            "offset": self.last_update_id + 1,
            "limit": 10,
            "timeout": 1  # Short timeout for polling
        }

        try:
            self.rate_limiter.wait_if_needed()
            response = requests.get(url, params=params, timeout=TELEGRAM_REQUEST_TIMEOUT)
            response.raise_for_status()

            data = response.json()
            if data.get("ok") and data.get("result"):
                updates = data["result"]
                if updates:
                    # Update last_update_id to avoid processing same messages
                    self.last_update_id = max(update["update_id"] for update in updates)
                return updates
            return []

        except requests.exceptions.RequestException as e:
            LOGGER.debug(f"Failed to get updates: {e}")
            return []
