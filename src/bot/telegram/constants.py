"""Constants and configuration for the Telegram bot module."""
import os
import tomllib
from pathlib import Path
from typing import Optional

from src.logger import get_logger

LOGGER = get_logger(__name__)


def _load_telegram_config_from_toml() -> dict:
    """Load Telegram configuration from config.toml file.
    
    Returns:
        dict: Telegram configuration dictionary, empty if not found or error occurs.
    """
    try:
        # Get project root and config path
        project_root = Path(__file__).parent.parent.parent
        config_path = project_root / "config.toml"
        
        if not config_path.exists():
            LOGGER.debug("config.toml not found, using environment variables only")
            return {}
            
        with open(config_path, "rb") as f:
            config_data = tomllib.load(f)
            
        return config_data.get("telegram", {})
    except Exception as e:
        LOGGER.debug(f"Failed to load Telegram config from TOML: {e}")
        return {}


def _get_telegram_token() -> Optional[str]:
    """Get Telegram bot token from environment or config file.
    
    Priority:
    1. TELEGRAM_BOT_TOKEN environment variable
    2. telegram.token in config.toml
    
    Returns:
        Optional[str]: Telegram bot token or None if not found.
    """
    # First try environment variable
    token = os.getenv("TELEGRAM_BOT_TOKEN")
    if token:
        return token
    
    # Then try config.toml
    config = _load_telegram_config_from_toml()
    return config.get("token")


def _get_telegram_chat_id() -> Optional[str]:
    """Get Telegram chat ID from environment or config file.

    Priority:
    1. TELEGRAM_CHAT_ID environment variable
    2. telegram.chat_id in config.toml

    Returns:
        Optional[str]: Telegram chat ID or None if not found.
    """
    # First try environment variable
    chat_id = os.getenv("TELEGRAM_CHAT_ID")
    if chat_id:
        return chat_id

    # Then try config.toml
    config = _load_telegram_config_from_toml()
    return config.get("chat_id")


def _is_telegram_enabled() -> bool:
    """Check if Telegram is enabled in configuration.

    Returns:
        bool: True if Telegram is enabled, False otherwise.
    """
    # If environment variables are set, assume enabled
    if os.getenv("TELEGRAM_BOT_TOKEN") and os.getenv("TELEGRAM_CHAT_ID"):
        return True

    # Check config.toml
    config = _load_telegram_config_from_toml()
    return config.get("enabled", False)


# Load configuration
TELEGRAM_TOKEN = _get_telegram_token()
TELEGRAM_CHAT_ID = _get_telegram_chat_id()
TELEGRAM_ENABLED = _is_telegram_enabled()

# Telegram API configuration
TELEGRAM_API_BASE_URL = "https://api.telegram.org/bot"
TELEGRAM_REQUEST_TIMEOUT = 10

# Rate limiting constants
RATE_LIMIT_MESSAGES_PER_SECOND = 1.0
RATE_LIMIT_MESSAGES_PER_MINUTE = 40
