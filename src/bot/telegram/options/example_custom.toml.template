# Example Custom Option Template
# Copy this file and modify it to create your own custom menu options
# Remove the .template extension and place in src/bot/telegram/options/

[option]
id = "example_custom"                    # Unique identifier (no spaces)
display_text = "🎨 Example Custom Option"  # Text shown in menu
description = "This is an example of how to create custom menu options"  # Description shown to user
type = "strategies_only"                 # Option type (see below for types)

# Option Types:
# - "use_config_file": Use current config.toml without changes
# - "strategies_only": Replace strategies with the ones defined below
# - "interactive_full": Launch interactive builder for full configuration
# - "interactive_strategies": Launch interactive builder for strategies only

# For type = "strategies_only", define your strategies below:
# Each strategy needs: ticker, name, reinvest_dividends, timing

[strategies.EXAMPLE_STRATEGY_1]
ticker = "AAPL"                         # Stock ticker symbol
name = "Apple Inc."                     # Display name in animation
reinvest_dividends = true               # true = reinvest, false = cash dividends
timing = "regular"                      # "regular", "peaks", or "lows"

[strategies.EXAMPLE_STRATEGY_2]
ticker = "MSFT"
name = "Microsoft Corporation"
reinvest_dividends = false
timing = "regular"

[strategies.EXAMPLE_STRATEGY_3]
ticker = "^GSPC"                        # Market indices start with ^
name = "S&P 500 Index"
reinvest_dividends = false
timing = "peaks"                        # Buy only at monthly peaks

# You can add as many strategies as you want
# Common ticker examples:
# - Individual stocks: AAPL, MSFT, GOOGL, AMZN, TSLA, JNJ, KO, etc.
# - Market indices: ^GSPC (S&P 500), ^IXIC (Nasdaq), ^DJI (Dow Jones)
# - Cryptocurrencies: BTC-USD, ETH-USD, ADA-USD, etc.
# - ETFs: SPY, QQQ, VTI, VXUS, etc.

# Timing options:
# - "regular": Invest at regular intervals (daily, weekly, monthly, etc.)
# - "peaks": Only invest when price is at monthly high
# - "lows": Only invest when price is at monthly low

# Example configurations for different scenarios:

# Tech vs Traditional:
# [strategies.TECH_STOCK]
# ticker = "AAPL"
# name = "Apple (Tech)"
# reinvest_dividends = true
# timing = "regular"
#
# [strategies.TRADITIONAL_STOCK]
# ticker = "JNJ"
# name = "Johnson & Johnson (Traditional)"
# reinvest_dividends = true
# timing = "regular"

# Growth vs Value:
# [strategies.GROWTH_STOCK]
# ticker = "TSLA"
# name = "Tesla (Growth)"
# reinvest_dividends = false
# timing = "regular"
#
# [strategies.VALUE_STOCK]
# ticker = "BRK-B"
# name = "Berkshire Hathaway (Value)"
# reinvest_dividends = false
# timing = "regular"

# International Diversification:
# [strategies.US_MARKET]
# ticker = "VTI"
# name = "US Total Market"
# reinvest_dividends = true
# timing = "regular"
#
# [strategies.INTERNATIONAL_MARKET]
# ticker = "VXUS"
# name = "International Markets"
# reinvest_dividends = true
# timing = "regular"
