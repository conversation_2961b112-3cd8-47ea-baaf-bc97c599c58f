"""Interactive strategy builder for Telegram menu system."""

import time
from typing import Dict, List, Any, Optional

from src.logger import get_logger
from src.bot.telegram.telegram import TelegramManager

LOGGER = get_logger(__name__)


class InteractiveStrategyBuilder:
    """
    Interactive builder for creating custom investment strategies via Telegram.
    """

    def __init__(self, telegram_manager: TelegramManager, timeout_seconds: int = 300):
        """
        Initialize the interactive strategy builder.

        Args:
            telegram_manager (TelegramManager): Telegram manager instance.
            timeout_seconds (int): Seconds to wait for each user response.
        """
        LOGGER.activate()
        self.telegram_manager = telegram_manager
        self.timeout_seconds = timeout_seconds
        self.strategies = {}
        self.general_config = {}
        
        LOGGER.info(f"🏗️ Interactive strategy builder initialized")

    def build_strategies_only(self, base_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Build custom strategies while keeping general config from base.

        Args:
            base_config (Dict[str, Any]): Base configuration to preserve.

        Returns:
            Dict[str, Any]: Configuration with custom strategies.
        """
        LOGGER.info("📋 Starting interactive strategy building (strategies only)")
        
        self.telegram_manager.send_message(
            "🏗️ **Custom Strategy Builder**\n\n"
            "Let's build your custom investment strategies!\n"
            "I'll keep your current general settings and only ask about strategies.\n\n"
            "⏱️ You have 5 minutes to respond to each question."
        )
        
        # Build strategies interactively
        self._build_strategies_interactive()
        
        if not self.strategies:
            LOGGER.warning("⚠️ No strategies created, falling back to default")
            return base_config
        
        # Merge with base config
        result_config = dict(base_config)
        result_config["strategies"] = self.strategies
        
        LOGGER.info(f"✅ Built {len(self.strategies)} custom strategies")
        return result_config

    def build_full_config(self, base_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Build full custom configuration including general settings and strategies.

        Args:
            base_config (Dict[str, Any]): Base configuration for fallback.

        Returns:
            Dict[str, Any]: Complete custom configuration.
        """
        LOGGER.info("🎨 Starting interactive full configuration building")
        
        self.telegram_manager.send_message(
            "🎨 **Full Custom Configuration Builder**\n\n"
            "Let's build your complete custom configuration!\n"
            "I'll ask about general settings first, then strategies.\n\n"
            "⏱️ You have 5 minutes to respond to each question."
        )
        
        # Build general config first
        if not self._build_general_config_interactive():
            LOGGER.warning("⚠️ General config building failed, using base config")
            self.general_config = base_config.get("general", {})
        
        # Build strategies
        self._build_strategies_interactive()
        
        if not self.strategies:
            LOGGER.warning("⚠️ No strategies created, falling back to default")
            return base_config
        
        # Create result config
        result_config = dict(base_config)
        if self.general_config:
            result_config["general"] = self.general_config
        result_config["strategies"] = self.strategies
        
        LOGGER.info(f"✅ Built full custom configuration with {len(self.strategies)} strategies")
        return result_config

    def _build_general_config_interactive(self) -> bool:
        """
        Build general configuration interactively.

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            # Years ago
            years_ago = self._ask_numeric_question(
                "📅 **Years of historical data**\n\n"
                "How many years back should we analyze?\n"
                "Example: 10 (for 10 years of data)\n"
                "Default: 20",
                default_value=20,
                min_value=1,
                max_value=50
            )
            
            # Investment amount
            investment_amount = self._ask_numeric_question(
                "💰 **Investment amount per period**\n\n"
                "How much do you want to invest each period?\n"
                "Example: 1000 (for $1000)\n"
                "Default: 1000",
                default_value=1000,
                min_value=1,
                max_value=1000000
            )
            
            # Investment frequency
            investment_kind = self._ask_choice_question(
                "📊 **Investment frequency**\n\n"
                "How often do you want to invest?\n\n"
                "1. Daily\n"
                "2. Weekly\n"
                "3. Monthly\n"
                "4. Quarterly\n"
                "5. Yearly\n\n"
                "Default: Monthly (3)",
                choices=["daily", "weekly", "monthly", "quarterly", "yearly"],
                default_choice="monthly"
            )
            
            # Animation duration
            animation_duration = self._ask_numeric_question(
                "🎬 **Animation duration**\n\n"
                "How long should the animation be (in seconds)?\n"
                "Example: 15 (for 15 seconds)\n"
                "Default: 20",
                default_value=20,
                min_value=5,
                max_value=120
            )
            
            self.general_config = {
                "years_ago": years_ago,
                "investment_amount": investment_amount,
                "investment_kind": investment_kind,
                "animation_duration_seconds": animation_duration,
                "fps": 30,  # Keep default
                "tax_rate": 0.26375,  # Keep default
                "tax_free_return_threshold_per_annu": 1000,  # Keep default
                "save_data": False  # Keep default
            }
            
            return True
            
        except Exception as e:
            LOGGER.error(f"❌ Error building general config: {e}")
            return False

    def _build_strategies_interactive(self):
        """Build strategies interactively."""
        strategy_count = 1
        
        while True:
            try:
                self.telegram_manager.send_message(
                    f"📈 **Strategy #{strategy_count}**\n\n"
                    "Let's create your investment strategy!"
                )
                
                # Get ticker
                ticker = self._ask_text_question(
                    "🏷️ **Stock ticker symbol**\n\n"
                    "Enter the ticker symbol (e.g., AAPL, MSFT, ^GSPC)\n"
                    "Examples:\n"
                    "• AAPL (Apple)\n"
                    "• MSFT (Microsoft)\n"
                    "• ^GSPC (S&P 500)\n"
                    "• BTC-USD (Bitcoin)"
                )
                
                if not ticker:
                    break
                
                # Get name
                name = self._ask_text_question(
                    f"📝 **Display name for {ticker}**\n\n"
                    "Enter a friendly name for this strategy\n"
                    f"Example: Apple Inc. or S&P 500\n"
                    f"Default: {ticker}",
                    default_value=ticker
                )
                
                # Get dividend reinvestment
                reinvest_dividends = self._ask_yes_no_question(
                    "💰 **Dividend reinvestment**\n\n"
                    "Should dividends be automatically reinvested?\n\n"
                    "• Yes: Dividends buy more shares\n"
                    "• No: Dividends are received as cash\n\n"
                    "Default: No"
                )
                
                # Get timing strategy
                timing = self._ask_choice_question(
                    "⏰ **Investment timing strategy**\n\n"
                    "When should investments be made?\n\n"
                    "1. Regular - Invest at regular intervals\n"
                    "2. Peaks - Only invest at monthly price peaks\n"
                    "3. Lows - Only invest at monthly price lows\n\n"
                    "Default: Regular (1)",
                    choices=["regular", "peaks", "lows"],
                    default_choice="regular"
                )
                
                # Create strategy
                strategy_id = f"{ticker}_{strategy_count}"
                self.strategies[strategy_id] = {
                    "ticker": ticker,
                    "name": name,
                    "reinvest_dividends": reinvest_dividends,
                    "timing": timing
                }
                
                self.telegram_manager.send_message(
                    f"✅ **Strategy #{strategy_count} created!**\n\n"
                    f"• Ticker: {ticker}\n"
                    f"• Name: {name}\n"
                    f"• Dividends: {'Reinvested' if reinvest_dividends else 'Cash'}\n"
                    f"• Timing: {timing.title()}"
                )
                
                # Ask if user wants to add another strategy
                add_another = self._ask_yes_no_question(
                    "➕ **Add another strategy?**\n\n"
                    "Do you want to add another investment strategy?\n\n"
                    "• Yes: Add another strategy\n"
                    "• No: Finish and create animation\n\n"
                    "Default: No"
                )
                
                if not add_another:
                    break
                
                strategy_count += 1
                
                # Limit to reasonable number of strategies
                if strategy_count > 10:
                    self.telegram_manager.send_message(
                        "⚠️ Maximum of 10 strategies reached. Finishing strategy creation."
                    )
                    break
                    
            except Exception as e:
                LOGGER.error(f"❌ Error building strategy #{strategy_count}: {e}")
                break

    def _ask_text_question(self, question: str, default_value: str = None) -> Optional[str]:
        """Ask a text question and wait for response."""
        self.telegram_manager.send_message(question)
        
        response = self._wait_for_response()
        if response is None:
            if default_value:
                self.telegram_manager.send_message(f"⏰ No response received, using default: {default_value}")
                return default_value
            return None
        
        return response.strip()

    def _ask_numeric_question(self, question: str, default_value: int, min_value: int = None, max_value: int = None) -> int:
        """Ask a numeric question and wait for response."""
        self.telegram_manager.send_message(question)
        
        response = self._wait_for_response()
        if response is None:
            self.telegram_manager.send_message(f"⏰ No response received, using default: {default_value}")
            return default_value
        
        try:
            value = int(response.strip())
            if min_value is not None and value < min_value:
                self.telegram_manager.send_message(f"⚠️ Value too low, using minimum: {min_value}")
                return min_value
            if max_value is not None and value > max_value:
                self.telegram_manager.send_message(f"⚠️ Value too high, using maximum: {max_value}")
                return max_value
            return value
        except ValueError:
            self.telegram_manager.send_message(f"⚠️ Invalid number, using default: {default_value}")
            return default_value

    def _ask_yes_no_question(self, question: str, default_yes: bool = False) -> bool:
        """Ask a yes/no question and wait for response."""
        self.telegram_manager.send_message(question + "\n\nReply with 'y' or 'n'")
        
        response = self._wait_for_response()
        if response is None:
            default_text = "Yes" if default_yes else "No"
            self.telegram_manager.send_message(f"⏰ No response received, using default: {default_text}")
            return default_yes
        
        response_lower = response.strip().lower()
        if response_lower in ['y', 'yes', '1', 'true']:
            return True
        elif response_lower in ['n', 'no', '0', 'false']:
            return False
        else:
            default_text = "Yes" if default_yes else "No"
            self.telegram_manager.send_message(f"⚠️ Invalid response, using default: {default_text}")
            return default_yes

    def _ask_choice_question(self, question: str, choices: List[str], default_choice: str) -> str:
        """Ask a multiple choice question and wait for response."""
        self.telegram_manager.send_message(question)
        
        response = self._wait_for_response()
        if response is None:
            self.telegram_manager.send_message(f"⏰ No response received, using default: {default_choice}")
            return default_choice
        
        response_stripped = response.strip()
        
        # Try to parse as number (1-based)
        try:
            choice_num = int(response_stripped)
            if 1 <= choice_num <= len(choices):
                return choices[choice_num - 1]
        except ValueError:
            pass
        
        # Try to match choice directly
        response_lower = response_stripped.lower()
        for choice in choices:
            if choice.lower() == response_lower:
                return choice
        
        self.telegram_manager.send_message(f"⚠️ Invalid choice, using default: {default_choice}")
        return default_choice

    def _wait_for_response(self) -> Optional[str]:
        """Wait for user response with timeout."""
        start_time = time.time()
        
        while (time.time() - start_time) < self.timeout_seconds:
            updates = self.telegram_manager._get_updates()
            
            for update in updates:
                if "message" in update and "text" in update["message"]:
                    return update["message"]["text"]
            
            time.sleep(1)
        
        return None
