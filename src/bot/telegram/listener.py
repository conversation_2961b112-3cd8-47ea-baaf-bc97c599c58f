# bot/listener.py
import time
import requests
import logging
from typing import Callable, Dict, Any

from src.logger import get_logger
from .telegram import TelegramManager
from ._rate_limiter import TelegramRateLimiter
from .constants import TELEGRAM_REQUEST_TIMEOUT


LOGGER = get_logger(__name__)


class TelegramListener:
    """
    Listens for user commands and interactions, handles interactive sessions,
    and triggers actions based on user input or timeouts.
    """

    def __init__(self, manager: TelegramManager, rate_limiter: TelegramRateLimiter):
        """
        Initializes the listener.

        Args:
            manager (TelegramManager): An instance of TelegramManager for sending messages.
            rate_limiter (TelegramRateLimiter): An instance for respecting API rate limits.
        """
        LOGGER.activate()

        self.manager = manager
        self.rate_limiter = rate_limiter
        self.base_url = manager.base_url
        self.last_update_id = 0

        # Stores active sessions, e.g., {chat_id: {"start_time": ..., "handler": ...}}
        self.active_sessions: Dict[int, Dict[str, Any]] = {}
        logging.info("🎧 Telegram Listener initialized.")

    def _get_updates(self) -> list:
        """Fetches new updates from the Telegram API using long polling."""
        url = f"{self.base_url}/getUpdates"
        # Use a longer timeout for long polling
        params = {"offset": self.last_update_id + 1, "timeout": 30}
        try:
            self.rate_limiter.wait_if_needed()
            response = requests.get(url, params=params, timeout=40)
            response.raise_for_status()
            updates = response.json().get("result", [])
            if updates:
                # Update the offset to avoid processing the same message again
                self.last_update_id = updates[-1]["update_id"]
            return updates
        except requests.exceptions.RequestException as e:
            logging.error(f"Error fetching updates: {e}")
            return []

    def _send_options(self, chat_id: int, text: str, options: Dict[str, str]):
        """
        Sends a message with inline keyboard buttons.

        Args:
            chat_id (int): The target chat ID.
            text (str): The message text to display above the buttons.
            options (Dict[str, str]): A dictionary where keys are button labels
                                     and values are the callback data.
        """
        keyboard = {
            "inline_keyboard": [
                [{"text": label, "callback_data": callback}]
                for label, callback in options.items()
            ]
        }
        url = f"{self.manager.base_url}/sendMessage"
        payload = {"chat_id": chat_id, "text": text, "reply_markup": keyboard}
        try:
            self.rate_limiter.wait_if_needed()
            response = requests.post(
                url, json=payload, timeout=TELEGRAM_REQUEST_TIMEOUT
            )
            response.raise_for_status()
            logging.info(f"Sent options to chat {chat_id}.")
        except requests.exceptions.RequestException as e:
            logging.error(f"Failed to send options to chat {chat_id}: {e}")

    def _handle_start_command(self, chat_id: int, action_handler: Callable):
        """Handles the /start command, beginning an interactive session."""
        logging.info(f"Received /start command from chat {chat_id}.")
        self.active_sessions[chat_id] = {
            "start_time": time.time(),
            "handler": action_handler,
        }
        options = {
            "Invest Monthly": "run_monthly",
            "Invest Weekly": "run_weekly",
            "Buy the Dips (Monthly Lows)": "run_lows",
            "Buy the Peaks (Monthly Highs)": "run_peaks",
        }
        self._send_options(
            chat_id,
            "Welcome! Please choose an investment strategy to simulate:",
            options,
        )

    def _handle_callback_query(self, query: dict):
        """Handles a button click from an inline keyboard."""
        chat_id = query["message"]["chat"]["id"]
        callback_data = query["data"]

        if chat_id in self.active_sessions:
            logging.info(f"Received callback '{callback_data}' from chat {chat_id}.")
            handler = self.active_sessions[chat_id]["handler"]

            # Trigger the action handler with the user's choice
            handler(callback_data)

            # End the session
            del self.active_sessions[chat_id]

            # Acknowledge the button press
            ack_url = f"{self.base_url}/answerCallbackQuery"
            requests.post(ack_url, json={"callback_query_id": query["id"]})
        else:
            logging.warning(
                f"Received callback from expired or unknown session: {chat_id}"
            )

    def listen(self, action_handler: Callable, timeout_seconds: int = 3600):
        """
        Starts the main polling loop to listen for messages and handle sessions.

        Args:
            action_handler (Callable): A function to call with the user's selection.
            timeout_seconds (int): Seconds to wait for a user response before falling back.
        """
        logging.info("Listener started. Waiting for commands...")
        while True:
            updates = self._get_updates()
            for update in updates:
                if "message" in update and "text" in update["message"]:
                    chat_id = update["message"]["chat"]["id"]
                    text = update["message"]["text"]
                    if text.startswith("/start"):
                        self._handle_start_command(chat_id, action_handler)

                elif "callback_query" in update:
                    self._handle_callback_query(update["callback_query"])

            # Check for and handle timed-out sessions
            now = time.time()
            timed_out_sessions = []
            for chat_id, session in self.active_sessions.items():
                if now - session["start_time"] > timeout_seconds:
                    logging.info(
                        f"Session for chat {chat_id} timed out. Using fallback."
                    )
                    handler = session["handler"]
                    handler("fallback_monthly")  # Trigger fallback action
                    timed_out_sessions.append(chat_id)

            for chat_id in timed_out_sessions:
                self.manager.send_message(
                    "You didn't make a selection in time. "
                    "Running the default 'Invest Monthly' simulation."
                )
                del self.active_sessions[chat_id]

            time.sleep(1)  # Small delay to prevent a tight loop on error
