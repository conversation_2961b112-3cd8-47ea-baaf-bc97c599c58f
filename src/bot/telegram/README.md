# 🤖 ReelStonks Telegram Bot Module

The Telegram bot module provides comprehensive integration with Telegram for sending notifications, logging information, and monitoring your ReelStonks script execution.

## ✨ Features

- **📱 Automatic Logging**: All log messages are automatically sent to your Telegram chat
- **🚀 Script Notifications**: Get notified when your script starts and completes
- **📊 Statistics Reporting**: Monitor message sending statistics and bot health
- **⚡ Rate Limiting**: Built-in rate limiting to respect Telegram API limits
- **🔄 Retry Logic**: Automatic retry with exponential backoff for failed messages
- **📝 Message Batching**: Efficiently batch multiple log messages together
- **🛡️ Error Handling**: Graceful degradation when Telegram is unavailable
- **🎨 Rich Formatting**: Messages include emojis and HTML formatting for better readability

## 🚀 Quick Setup

### 1. Create a Telegram Bot

1. Open Telegram and search for `@BotFather`
2. Send `/newbot` and follow the instructions
3. Save your bot token (looks like `123456789:ABCdefGHIjklMNOpqrsTUVwxyz`)

### 2. Get Your Chat ID

1. Send a message to your bot
2. Visit `https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates`
3. Look for `"chat":{"id":123456789}` in the response
4. Save your chat ID (the number after `"id":`)

### 3. Configure ReelStonks

**Option A: Environment Variables (Recommended)**
```bash
export TELEGRAM_BOT_TOKEN="your_bot_token_here"
export TELEGRAM_CHAT_ID="your_chat_id_here"
```

**Option B: Configuration File**
Add to your `config.toml`:
```toml
[telegram]
token = "your_bot_token_here"
chat_id = "your_chat_id_here"
enabled = true
```

## 📖 Usage

### Basic Message Sending

```python
from src.bot import send_message

# Send a simple message
send_message("Hello from ReelStonks! 🚀")
```

### Advanced Features

```python
from src.bot import get_telegram_manager, setup_telegram_logging

# Get the manager for advanced features
manager = get_telegram_manager()

# Send script notifications
manager.send_script_start_notification("MyScript")
manager.send_script_completion_notification("MyScript", success=True, duration=123.45)

# Send statistics
manager.send_statistics_summary()

# Set up automatic logging
setup_telegram_logging()  # Now all log messages go to Telegram!
```

### Automatic Integration

When you run `main.py`, Telegram integration is automatically enabled if configured:

```python
# This happens automatically in main.py
from src.bot import setup_telegram_logging, is_telegram_configured

if is_telegram_configured():
    setup_telegram_logging()
    # All subsequent log messages will be sent to Telegram
```

## 🧪 Testing Your Setup

Run the test suite to verify everything works:

```bash
python -m src.bot.example_usage
```

This will test:
- ✅ Basic message sending
- ✅ Script notifications
- ✅ Logging integration
- ✅ Error handling
- ✅ Message truncation

## ⚙️ Configuration Options

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `TELEGRAM_BOT_TOKEN` | Your bot token from @BotFather | Yes |
| `TELEGRAM_CHAT_ID` | Your chat ID or group chat ID | Yes |

### Config.toml Options

```toml
[telegram]
token = "your_bot_token_here"        # Bot token
chat_id = "your_chat_id_here"        # Chat ID
enabled = true                       # Enable/disable Telegram features
```

### Advanced Constants

You can modify these in `src/bot/constants.py`:

```python
MAX_MESSAGE_LENGTH = 4096           # Telegram's message limit
MESSAGE_BATCH_SIZE = 10             # Messages to batch together
MESSAGE_BATCH_TIMEOUT = 5           # Seconds to wait for batch
RATE_LIMIT_MESSAGES_PER_MINUTE = 20 # Rate limiting
TELEGRAM_LOG_LEVEL_THRESHOLD = "INFO" # Minimum log level to send
```

## 📱 What You'll Receive

When running ReelStonks with Telegram enabled, you'll receive:

1. **🚀 Start Notification**: "ReelStonks started at 2024-01-15 10:30:00"
2. **📝 Log Messages**: All INFO, WARNING, ERROR, and CRITICAL messages
3. **✅ Success Notification**: "ReelStonks completed successfully at 10:35:00 ⏱️ Duration: 5m 0s"
4. **❌ Error Notifications**: If something goes wrong, you'll know immediately

### Example Messages

```
🚀 ReelStonks started at 2024-01-15 10:30:00

ℹ️ 10:30:01 [INFO] main: 🚀 Initializing ReelStonks...
ℹ️ 10:30:02 [INFO] main: 📋 Loading configuration file...
ℹ️ 10:30:05 [INFO] main: Processing strategy 'AAPL_no_reinvest' for AAPL ...

✅ ReelStonks completed successfully at 10:35:00
⏱️ Duration: 5m 0s
```

## 🛠️ API Reference

### Functions

- `send_message(message: str) -> bool`: Send a message to Telegram
- `get_telegram_manager() -> TelegramManager`: Get the global manager instance
- `setup_telegram_logging(logger_name: str = None) -> bool`: Enable Telegram logging
- `is_telegram_configured() -> bool`: Check if Telegram is configured

### Classes

- `TelegramManager`: Main class for Telegram operations
- `TelegramLogHandler`: Custom logging handler for Telegram
- `TelegramLogFormatter`: Formatter with emojis and HTML formatting
- `RateLimiter`: Rate limiting for API calls

## 🔧 Troubleshooting

### Common Issues

**"Telegram not configured"**
- Check your bot token and chat ID
- Verify environment variables or config.toml settings
- Make sure `enabled = true` in config.toml

**"Failed to send message"**
- Check your internet connection
- Verify your bot token is correct
- Make sure your chat ID is correct (try sending a message to the bot first)

**"Rate limited"**
- The bot respects Telegram's rate limits
- Messages will be queued and sent when possible
- Consider reducing log verbosity if needed

### Debug Mode

Enable debug logging to see what's happening:

```python
import logging
logging.getLogger('src.bot').setLevel(logging.DEBUG)
```

## 🤝 Contributing

The Telegram bot module is designed to be extensible. You can:

- Add new message types
- Implement custom formatters
- Add new notification triggers
- Extend the rate limiting logic

See the source code in `src/bot/` for implementation details.
