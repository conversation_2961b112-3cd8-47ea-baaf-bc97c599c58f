# bot/rate_limiter.py
import time
from collections import deque
from src.logger import get_logger
from src.bot.telegram.constants import (
    RATE_LIMIT_MESSAGES_PER_SECOND,
    RATE_LIMIT_MESSAGES_PER_MINUTE
)


LOGGER = get_logger(__name__)


class TelegramRateLimiter:
    """
    A rate limiter to control message sending frequency to the Telegram API.

    This class ensures that the number of messages sent does not exceed the
    per-second and per-minute limits defined in the application's constants.
    """

    def __init__(self, messages_per_second: float = RATE_LIMIT_MESSAGES_PER_SECOND,
                 messages_per_minute: int = RATE_LIMIT_MESSAGES_PER_MINUTE):
        """
        Initializes the rate limiter with specific limits.

        Args:
            messages_per_second (float): Maximum messages allowed per second.
            messages_per_minute (int): Maximum messages allowed per minute.
        """
        self.interval_per_second = 1.0 / messages_per_second
        self.messages_per_minute = messages_per_minute
        
        # Stores timestamps of recent messages to track the per-minute rate
        self.message_times = deque()
        # Tracks the time of the last message to enforce the per-second rate
        self.last_send_time = 0.0
        LOGGER.info(
            f"Rate limiter initialized with {messages_per_second}/sec and "
            f"{messages_per_minute}/min limits."
        )

    def _cleanup_old_timestamps(self):
        """Removes timestamps older than 60 seconds from the deque."""
        now = time.time()
        cutoff = now - 60
        while self.message_times and self.message_times[0] < cutoff:
            self.message_times.popleft()

    def wait_if_needed(self):
        """
        Blocks execution (sleeps) for the necessary duration to respect rate limits.
        This should be called before sending a message.
        """
        self._cleanup_old_timestamps()

        # Calculate required wait time ---
        now = time.time()

        # Per-second wait time
        wait_sec = self.interval_per_second - (now - self.last_send_time)
        
        # Per-minute wait time
        wait_min = 0.0
        if len(self.message_times) >= self.messages_per_minute:
            # Time until the oldest message in the window expires
            wait_min = self.message_times[0] + 60 - now

        # Determine the longest required wait time
        final_wait_time = max(wait_sec, wait_min)

        if final_wait_time > 0:
            LOGGER.debug(f"Rate limit triggered. Waiting for {final_wait_time:.2f} seconds.")
            time.sleep(final_wait_time)
        
        # Record the current send time after waiting
        self.last_send_time = time.time()
        self.message_times.append(self.last_send_time)

