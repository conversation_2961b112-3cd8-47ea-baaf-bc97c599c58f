"""Interactive Telegram menu system for ReelStonks strategy selection."""

import time
import threading
from typing import List, Optional, Dict, Any

from src.logger import get_logger
from src.bot.telegram.telegram import TelegramManager
from src.bot.telegram.constants import DEFAULT_INTERACTIVE_TIMEOUT_MINUTES
from src.bot.telegram.option_loader import OptionLoader, MenuOption
from src.bot.telegram.strategy_builder import InteractiveStrategyBuilder

LOGGER = get_logger(__name__)


class InteractiveMenu:
    """
    Interactive Telegram menu system that presents options to users
    and waits for their selection within a configurable timeout.
    """

    def __init__(self, telegram_manager: TelegramManager, timeout_minutes: int = DEFAULT_INTERACTIVE_TIMEOUT_MINUTES):
        """
        Initialize the interactive menu system.

        Args:
            telegram_manager (TelegramManager): Telegram manager instance for sending messages.
            timeout_minutes (int): Minutes to wait for user response before using default.
        """
        LOGGER.activate()
        self.telegram_manager = telegram_manager
        self.timeout_minutes = timeout_minutes
        self.timeout_seconds = timeout_minutes * 60
        self.user_selection = None
        self.selection_made = False
        self.menu_options: List[MenuOption] = []
        self.option_loader = OptionLoader()
        self.strategy_builder = InteractiveStrategyBuilder(telegram_manager, timeout_seconds=300)

        LOGGER.info(f"🎛️ Interactive menu initialized with {timeout_minutes} minute timeout")

    def load_options_from_toml(self):
        """Load menu options from TOML files."""
        self.menu_options = self.option_loader.load_all_options()
        LOGGER.info(f"� Loaded {len(self.menu_options)} options from TOML files")

    def format_menu_message(self) -> str:
        """
        Format the menu options into a readable message.

        Returns:
            str: Formatted menu message.
        """
        message_lines = [
            "🎯 ReelStonks Interactive Menu",
            "",
            f"⏱️ You have {self.timeout_minutes} minutes to choose an option.",
            "If no selection is made, the default configuration will be used.",
            "",
            "📋 Available Options:",
            ""
        ]

        for i, option in enumerate(self.menu_options, 1):
            message_lines.extend([
                f"{i}. {option.display_text}",
                f"   {option.description}",
                ""
            ])

        message_lines.extend([
            "💬 How to respond:",
            f"Reply with the number (1-{len(self.menu_options)}) of your choice.",
            f"Example: Send '1' for the first option.",
            "",
            "⚡ Quick tip: Option 1 is always the default configuration!"
        ])

        return "\n".join(message_lines)

    def wait_for_user_selection(self) -> Optional[MenuOption]:
        """
        Wait for user selection with timeout.

        Returns:
            Optional[MenuOption]: Selected option or None if timeout/default.
        """
        LOGGER.info(f"⏳ Waiting for user selection (timeout: {self.timeout_minutes} minutes)")
        
        # Start timeout timer
        timeout_thread = threading.Timer(self.timeout_seconds, self._handle_timeout)
        timeout_thread.start()
        
        try:
            # Poll for messages
            start_time = time.time()
            while not self.selection_made and (time.time() - start_time) < self.timeout_seconds:
                self._check_for_user_response()
                time.sleep(1)  # Check every second
                
        finally:
            timeout_thread.cancel()
        
        if self.user_selection:
            LOGGER.info(f"✅ User selected: {self.user_selection.display_text}")
            return self.user_selection
        else:
            LOGGER.info("⏰ No selection made, using default configuration")
            return None

    def _handle_timeout(self):
        """Handle timeout by setting selection_made flag."""
        if not self.selection_made:
            LOGGER.info("⏰ Interactive menu timed out")
            self.selection_made = True
            self.telegram_manager.send_message(
                f"⏰ Time's up! Using default configuration since no selection was made within {self.timeout_minutes} minutes."
            )

    def _check_for_user_response(self):
        """Check for user response and process selection."""
        try:
            # Get recent messages
            updates = self.telegram_manager._get_updates()
            
            for update in updates:
                if "message" in update and "text" in update["message"]:
                    text = update["message"]["text"].strip()
                    
                    # Try to parse as number
                    try:
                        selection_num = int(text)
                        if 1 <= selection_num <= len(self.menu_options):
                            self.user_selection = self.menu_options[selection_num - 1]
                            self.selection_made = True
                            
                            # Send confirmation
                            self.telegram_manager.send_message(
                                f"✅ Great choice! You selected:\n\n"
                                f"**{self.user_selection.display_text}**\n"
                                f"{self.user_selection.description}\n\n"
                                f"🚀 Starting animation creation..."
                            )
                            return
                        else:
                            self.telegram_manager.send_message(
                                f"❌ Invalid selection. Please choose a number between 1 and {len(self.menu_options)}."
                            )
                    except ValueError:
                        # Not a number, ignore or provide help
                        if text.lower() in ['help', '?']:
                            self.telegram_manager.send_message(self.format_menu_message())
                        
        except Exception as e:
            LOGGER.error(f"Error checking for user response: {e}")

    def show_menu_and_wait(self, base_config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Show the interactive menu and wait for user selection.

        Args:
            base_config (Dict[str, Any]): Base configuration for interactive builders.

        Returns:
            Optional[Dict[str, Any]]: Selected configuration or None for default.
        """
        if not self.menu_options:
            self.load_options_from_toml()

        # Send menu message
        menu_message = self.format_menu_message()
        self.telegram_manager.send_message(menu_message)

        # Wait for selection
        selected_option = self.wait_for_user_selection()

        if selected_option:
            return self._process_selected_option(selected_option, base_config)

        return None

    def _process_selected_option(self, option: MenuOption, base_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process the selected option and return the appropriate configuration.

        Args:
            option (MenuOption): Selected menu option.
            base_config (Dict[str, Any]): Base configuration.

        Returns:
            Dict[str, Any]: Processed configuration.
        """
        if option.option_type == "use_config_file":
            LOGGER.info("🔧 Using default configuration")
            return base_config

        elif option.option_type == "strategies_only":
            LOGGER.info(f"📋 Using predefined strategies: {option.option_id}")
            return option.strategy_data

        elif option.option_type == "interactive_strategies":
            LOGGER.info("🏗️ Starting interactive strategy builder (strategies only)")
            return self.strategy_builder.build_strategies_only(base_config)

        elif option.option_type == "interactive_full":
            LOGGER.info("🎨 Starting interactive full configuration builder")
            return self.strategy_builder.build_full_config(base_config)

        else:
            LOGGER.warning(f"⚠️ Unknown option type: {option.option_type}, using default")
            return base_config
