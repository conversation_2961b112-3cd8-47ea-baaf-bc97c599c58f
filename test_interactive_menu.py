#!/usr/bin/env python3
"""Test script for the interactive Telegram menu system."""

import tomllib
from src.bot import Telegram<PERSON>anager, TELEGRAM_ENABLED, INTERACTIVE_MENU_ENABLED, INTERACTIVE_TIMEOUT_MINUTES
from src.bot.telegram.interactive_menu import InteractiveMenu
from src.utils.config_merger import merge_strategy_config, validate_merged_config
from src.utils.paths import get_config_path
from src.logger import get_logger

LOGGER = get_logger(__file__)
LOGGER.activate()


def test_interactive_menu():
    """Test the interactive menu system."""
    
    if not TELEGRAM_ENABLED:
        print("❌ Telegram is not enabled. Please configure your bot token and chat ID.")
        return False
    
    if not INTERACTIVE_MENU_ENABLED:
        print("❌ Interactive menu is disabled in configuration.")
        return False
    
    print("🚀 Testing ReelStonks Interactive Menu System")
    print(f"⏱️ Timeout: {INTERACTIVE_TIMEOUT_MINUTES} minutes")
    
    # Load base configuration
    config_path = get_config_path()
    with open(config_path, "rb") as f:
        config_data = tomllib.load(f)
    
    # Initialize Telegram manager
    telegram_manager = TelegramManager()
    telegram_manager.send_message("🧪 Testing ReelStonks Interactive Menu System...")
    
    # Create and show interactive menu
    interactive_menu = InteractiveMenu(telegram_manager, INTERACTIVE_TIMEOUT_MINUTES)
    
    print("📋 Showing interactive menu to user...")
    selected_config = interactive_menu.show_menu_and_wait(config_data)
    
    if selected_config:
        print("✅ User made a selection")
        
        # Test configuration merging
        merged_config = merge_strategy_config(config_data, selected_config)
        
        if validate_merged_config(merged_config):
            print("✅ Configuration validation passed")
            
            # Show selected strategies
            if "strategies" in merged_config:
                print(f"📊 Selected strategies ({len(merged_config['strategies'])}):")
                for strategy_id, strategy_data in merged_config["strategies"].items():
                    print(f"  - {strategy_id}: {strategy_data.get('name', strategy_data.get('ticker', 'Unknown'))}")
            
            telegram_manager.send_message(
                f"✅ Test completed successfully!\n\n"
                f"Strategies: {len(merged_config.get('strategies', {}))}"
            )
            return True
        else:
            print("❌ Configuration validation failed")
            telegram_manager.send_message("❌ Test failed: Invalid configuration")
            return False
    else:
        print("⏰ No selection made, using default configuration")
        telegram_manager.send_message("⏰ Test completed: No selection made, default config used")
        return True


if __name__ == "__main__":
    success = test_interactive_menu()
    if success:
        print("🎉 Interactive menu test completed successfully!")
    else:
        print("💥 Interactive menu test failed!")
